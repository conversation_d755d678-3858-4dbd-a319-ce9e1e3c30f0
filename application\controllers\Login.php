<?php
defined( 'BASEPATH' ) or exit( 'No direct script access allowed' );
require_once('vendor/autoload.php');
use <PERSON><PERSON>it\Firebase\Factory;

class Login extends CI_Controller {

	function __construct() {
		parent::__construct();
		$firebase = (new Factory)->withServiceAccount('firebase_config.json');
        $this->auth = $firebase->createAuth();
		$this->load->database();
		$this->load->model( "user_model" );
		$this->load->model( "quiz_model" );
		$this->load->model( "result_model" );
		$this->load->helper( 'url' );
		$this->load->model( "language_model" );
		$this->load->helper( 'cookie' );
		$this->lang->load( 'basic',
			$this->language_model->get() );
		if ( $this->db->database == '' ) {
			$this->load->helper( 'url' );
			redirect( 'install' );
		}
	}

	public function index() {
		$this->load->helper( 'url' );
		if ( $this->session->userdata( 'logged_in' ) ) {
			$logged_in = $this->session->userdata( 'logged_in' );
			if ( $logged_in['su'] == '1' ) {
				redirect( 'dashboard' );
			} else {
				redirect( 'quiz' );
			}
		}
		$data['current'] = $this->language_model->get();
		$data['title'] = $this->lang->line( 'login' );
		$data['recent_quiz'] = $this->quiz_model->recent_quiz( '5' );
		$this->load->view( 'admin/login', $data );
	}
	public function change_language( $lang ) {
		$this->load->model( "language_model" );
		if ( $lang == "vi" ) {
			$this->language_model->set( "vietnamese" );
		} else {
			$this->language_model->set( "english" );
		}
		return true;
	}

	public function update_language( $lang ) {
		$this->load->model( "language_model" );
		if ( $lang == "vi" ) {
			$this->language_model->set( "vietnamese" );
			$this->language_model->updateLanguage( "vietnamese" );
		} else {
			$this->language_model->set( "english" );
			$this->language_model->updateLanguage( "english" );
		}
		return true;
	}

	public function update_user_group( $gid ) {
		$logged_in = $this->session->userdata( 'logged_in' );
		$userdata = array(
			'gid' => $gid,
			'thread_id' => '',
			'last_assistant_call' => '',
			'last_assistant_response' => ''
		);
		$this->db->where( 'uid', $logged_in['uid'] );
		if ( $this->db->update( 'savsoft_users', $userdata ) ) {
			$logged_in['gid'] = $gid;
			$this->session->set_userdata( 'logged_in', $logged_in );
			return true;
		} else {
			return false;
		}
	}

	public function resend() {
		if ( $this->input->post( 'email' ) ) {
			$status = $this->user_model->resend( $this->input->post( 'email' ) );
		}
		print_r( json_encode( 'success' ) );
	}

	public function resend_web() {
		if ( $this->input->get( 'email' ) ) {
			$status = $this->user_model->resend( $this->input->get( 'email' ) );
		}
		$this->session->set_flashdata( 'message', "<div class='alert alert-success'>" . $this->lang->line( 'resend_success' ) . " </div>" );
	}


	function addtocart( $gid, $gname, $price ) {
		$this->load->helper( 'url' );
		if ( $this->session->userdata( 'cart' ) ) {
			$d = $this->session->userdata( 'cart' );
			$d[] = array(
				$gid, $gname, $price
			);
		} else {
			$d = array();
			$d[] = array(
				$gid, $gname, $price
			);
		}
		$this->session->set_userdata( 'cart', $d );
		$this->session->set_flashdata( 'message', 'Group added into cart' );
		redirect( 'login/registration/' );
	}

	function clearcart() {
		$this->load->helper( 'url' );
		$this->session->unset_userdata( 'cart' );
		$this->session->set_flashdata( 'message', 'Cart cleared successfully' );
		redirect( 'login/pre_registration' );
	}

	function clearcartval( $gid ) {
		$this->load->helper( 'url' );
		$d = array();
		foreach ( $this->session->userdata( 'cart' ) as $k => $v ) {
			if ( $v[0] == $gid ) {
			} else {
				$d[] = $v;
			}
		}
		$this->session->set_userdata( 'cart', $d );
		$this->session->set_flashdata( 'message', 'Group removed from cart' );
		redirect( 'login/pre_registration' );
	}

	public function pre_registration() {
		$this->load->helper( 'url' );
		$data['title'] = $this->lang->line( 'select_package' );
		// fetching group list
		$data['group_list'] = $this->user_model->group_list();
		$this->load->view( 'header', $data );
		$this->load->view( 'pre_register', $data );
		$this->load->view( 'footer', $data );
	}


	public function registration( $gid = '0' ) {
		$this->load->helper( 'url' );
		$data['gid'] = $gid;
		$data['title'] = $this->lang->line( 'register_new_account' );
		$data['custom_form'] = $this->user_model->custom_form( 'Registration' );
		// fetching group list
		$data['group_list'] = $this->user_model->group_list();
		$this->load->view( 'header', $data );
		$this->load->view( 'register', $data );
		$this->load->view( 'footer', $data );
	}


	public function verifylogin( $p1 = '', $p2 = '' ) {
		if ( $this->session->userdata( 'login_diffrent_location' ) ) {
			$this->session->unset_userdata( 'login_diffrent_location' );
		}
		if ( $p1 == '' ) {
			$username = $this->input->post( 'email' );
			$password = $this->input->post( 'password' );
		} else {
			$username = urldecode( $p1 );
			$password = urldecode( $p2 );
		}
		$status = $this->user_model->login( $username, $password );
		if ( $status['status'] == '1' ) {
			$this->load->helper( 'url' );
			// row exist fetch userdata
			$user = $status['user'];
			$gids = $user['gid'];
			$uid = $user['uid'];

			$sl = "select * from savsoft_group where gid in ($gids) ";
			$rq = $this->db->query( $sl );

			$gr = $rq->result_array();

			$price = 0;

			foreach ( $gr as $pk => $pv ) {
				$gid = $pv['gid'];
				$sl2 = "select * from savsoft_payment where uid='$uid' and gid='$gid' and payment_status='Paid' ";

				$sl3 = $this->db->query( $sl2 );
				// echo $sl3->num_rows(); echo "<br>";

				if ( $sl3->num_rows() >= 1 ) {
				} else {
					$price += $pv['price'];
				}
			}

			// validate if user assigned to paid group
			if ( $price > '0' ) {

				// user assigned to paid group now validate expiry date.
				if ( $user['subscription_expired'] <= time() ) {
					// eubscription expired, redirect to payment page

					redirect( 'payment_gateway_2/subscribe/' . $gids . '/' . $user['uid'] );
				}
			}

			$user['base_url'] = base_url();
			// creating login cookie
			$this->session->set_userdata( 'show_mentor', isNotEmpty( $user['mentor'] ) );
			$this->session->set_userdata( 'logged_in', $user );
			$continueQuiz = $this->result_model->continue_quiz();
			if ( $continueQuiz ) {
				$this->session->set_flashdata( 'continue_quiz', $continueQuiz[0]['quid'] );
				$this->session->set_flashdata( 'continue_quiz_theme', $continueQuiz[0]['theme'] );
			}
			// redirect to dashboard
			if ( $user['su'] == '1' ) {
				redirect( 'user' );
			} else {
				$burl = $this->config->item( 'base_url' ) . 'userDashboard';
				header( "location:$burl" );
			}
		} else if ( $status['status'] == '0' ) {

			// invalid login
			// try to auth wp
			if ( $this->config->item( 'wp-login' ) ) {

				if ( $this->authentication( $username, $password ) ) {

					$this->verifylogin( $username, $password );
				} else {
					$this->load->helper( 'url' );
					$this->session->set_flashdata( 'message', $status['message'] );
					$burl = $this->config->item( 'base_url' );
					header( "location:$burl" );
				}
			} else {

				$this->load->helper( 'url' );
				$this->session->set_flashdata( 'message', $status['message'] );
				redirect( 'login' );
			}
		} else if ( $status['status'] == '2' ) {
			$this->load->helper( 'url' );


			// email not verified
			$this->session->set_flashdata( 'message', $status['message'] );
			redirect( 'login' );
		} else if ( $status['status'] == '3' ) {
			$this->load->helper( 'url' );


			// email not verified
			$this->session->set_flashdata( 'message', $status['message'] );
			redirect( 'login' );
		}
	}

	public function setTimeOut( $user ) {
		if ( $user['su'] == '1' ) {
			$this->session->sess_expiration = '1296000';
		} else {
			$this->session->sess_expiration = '7200';
		}
	}

	public function verifyloginajax( $p1 = '', $p2 = '' ) {
		if ( $this->session->userdata( 'login_diffrent_location' ) ) {
			$this->session->unset_userdata( 'login_diffrent_location' );
		}
		if ( $p1 == '' ) {
			$username = $this->input->post( 'email' );
			$password = $this->input->post( 'password' );
		} else {
			$username = urldecode( $p1 );
			$password = urldecode( $p2 );
		}
		$status = $this->user_model->login( $username, $password );
		if ( $status['user']['subscription_expired'] != 0 && $status['user']['subscription_expired'] != "0" ) {
			// Get the current date
			$currentDate = date( 'Y-m-d' );

			// Get the date from the timestamp
			$timestampDate = date( 'Y-m-d', $status['user']['subscription_expired'] );
			// Compare the two dates
			if ( $timestampDate < $currentDate ) {
				$this->user_model->update_status_user( $status['user']['uid'], 'Inactive' );
				echo json_encode( [ 'login' => 'error', 'data' => $this->lang->line( "subscription_expired_message" ) ] );
				die;
			}
		}

		if ( $status['status'] == '1' ) {
			$this->load->helper( 'url' );
			// row exist fetch userdata
			$user = $status['user'];
			$gids = $user['gid'];
			$uid = $user['uid'];
			$sl = "select * from savsoft_group where gid in ($gids) ";
			$rq = $this->db->query( $sl );

			$gr = $rq->result_array();

			$price = 0;
			foreach ( $gr as $pk => $pv ) {
				$gid = $pv['gid'];
				$sl2 = "select * from savsoft_payment where uid='$uid' and gid='$gid' and payment_status='Paid' ";

				$sl3 = $this->db->query( $sl2 );
				// echo $sl3->num_rows(); echo "<br>";

				if ( $sl3->num_rows() >= 1 ) {
				} else {
					$price += $pv['price'];
				}
			}

			// validate if user assigned to paid group
			if ( $price > '0' ) {

				// user assigned to paid group now validate expiry date.
				if ( $user['subscription_expired'] <= time() ) {
					// subscription expired, redirect to payment page
					echo json_encode( [ 'login' => 'ok', 'data' => 'payment_gateway_2/subscribe/' . $gids . '/' . $user['uid'] ] );
					//redirect('payment_gateway_2/subscribe/'.$gids.'/'.$user['uid']);

				}
			}

			$user['base_url'] = base_url();
			// creating login cookie
			$this->setTimeOut( $user );
			$this->user_model->set_show_roadmap( $user['gid'] );
			$this->user_model->set_user_groups( $user['gids'] );
			$this->session->set_userdata( 'logged_in', $user );
			$category = $this->db->where( "FIND_IN_SET('" . $user['gid'] . "', savsoft_category.category_gids)" )->get( 'savsoft_category' )->row_array();
			if ( isNotEmpty( $category["roadmap_vi"] ) == false && isNotEmpty( $category["roadmap_en"] ) == false ) {
				$this->session->set_userdata( 'road_map', true );
			} else {
				$this->session->set_userdata( 'road_map', false );
			}

			$continueQuiz = $this->result_model->continue_quiz( $user['uid'] );

			if ( $continueQuiz ) {
				$this->setTimeOut( $user );
				$this->session->set_flashdata( 'continue_quiz', $continueQuiz[0]['quid'] );
				$this->setTimeOut( $user );
				$this->session->set_flashdata( 'continue_quiz_theme', $continueQuiz[0]['theme'] );
			}
			// fix tạm thời khi vào dashboard xong logout luôn
			if ( $user['su'] != '1' && $this->session->userdata( 'referred_from' ) && str_contains( $this->session->userdata( 'referred_from' ), 'ai_message' ) == false ) {
				echo json_encode( [ 'login' => 'ok', 'data' => $this->session->userdata( 'referred_from' ) ] );
				return;
			}
			// redirect to dashboard
			if ( $user['su'] == '1' ) {
				echo json_encode( [ 'login' => 'ok', 'data' => 'user', 'changeLang' => $user["language"], 'name' => $user["name"] ] );
				//redirect('dashboard');

			} else {
				// Set User Lang theo setting
				/* if($user["language"] == "english"){
								$this->language_model->set("english");
							}else{
								$this->language_model->set("vietnamese");
							} */
				$burl = $this->config->item( 'base_url' ) . 'userDashboard';
				if ( $gids != $this->config->item( 'default_gid' ) ) {
					echo json_encode( [ 'login' => 'ok', 'data' => $burl, 'changeLang' => $user["language"], 'name' => $user["name"] ] );
				} else {
					echo json_encode( [ 'login' => 'ok', 'data' => $this->config->item( 'base_url' ) . 'quiz', 'changeLang' => $user["language"], 'name' => $user["name"] ] );
				}
				//header("location:$burl");
			}
		} else if ( $status['status'] == '0' ) {

			// invalid login
			// try to auth wp
			if ( $this->config->item( 'wp-login' ) ) {

				if ( $this->authentication( $username, $password ) ) {

					$this->verifylogin( $username, $password );
				} else {
					$this->load->helper( 'url' );
					$this->session->set_flashdata( 'message', $status['message'] );
					$burl = $this->config->item( 'base_url' );
					return json_encode( [ 'login' => 'error', 'data' => $status['message'] ] );
					//header("location:$burl");
				}
			} else {

				$this->load->helper( 'url' );
				$this->session->set_flashdata( 'message', $status['message'] );
				echo json_encode( [ 'login' => 'error', 'data' => $status['message'] ] );
			}
		} else if ( $status['status'] == '2' ) {
			$this->load->helper( 'url' );


			// email not verified
			$this->session->set_flashdata( 'message', $status['message'] );
			echo json_encode( [ 'login' => 'error', 'data' => $status['message'] ] );
			// redirect('login');
		} else if ( $status['status'] == '3' ) {
			$this->load->helper( 'url' );

			// email not verified
			$this->session->set_flashdata( 'message', $status['message'] );
			echo json_encode( [ 'login' => 'error', 'data' => $status['message'] ] );
			// redirect('login');
		}
	}

	function verify( $vcode ) {
		$this->load->helper( 'url' );
		if ( $this->user_model->verify_code( $vcode ) ) {
			$this->session->set_flashdata( 'message', $this->lang->line( 'verify_text' ) );
			redirect( 'login' );
		} else {
			$data['title'] = $this->lang->line( 'invalid_link' );
			$this->load->view( 'header', $data );
			$this->load->view( 'verify_code', $data );
			$this->load->view( 'footer', $data );
		}
	}

	public function insert_user_ajax() {
		$result = $this->user_model->insert_user_ajax();
		echo $result;
	}

	function forgot() {
		$this->load->helper( 'url' );
		if ( $this->input->post( 'email' ) ) {
			$user_email = $this->input->post( 'email' );
			if ( $this->user_model->reset_password( $user_email ) ) {
				$this->session->set_flashdata( 'message', "<div class='alert alert-success'>" . $this->lang->line( 'password_updated' ) . " </div>" );
			} else {
				$this->session->set_flashdata( 'message', "<div class='alert alert-danger'>" . $this->lang->line( 'email_doesnot_exist' ) . " </div>" );
			}
			redirect( 'login/forgot' );
		}

		$data['title'] = $this->lang->line( 'forgot_password' );
		$this->load->view( 'header', $data );
		$this->load->view( 'forgot_password', $data );
		$this->load->view( 'footer', $data );
	}

	public function insert_user() {
		$this->load->helper( 'url' );
		$this->load->library( 'form_validation' );
		$this->form_validation->set_rules( 'email', 'Email', 'required|is_unique[savsoft_users.email]' );
		$this->form_validation->set_rules( 'password', 'Password', 'required' );
		if ( $this->form_validation->run() == FALSE ) {
			$this->session->set_flashdata( 'message', "<div class='alert alert-danger'>" . validation_errors() . " </div>" );
			redirect( 'login/registration/' );
		} else {
			if ( $this->user_model->insert_user_2() ) {
				if ( $this->config->item( 'verify_email' ) ) {
					$this->session->set_flashdata( 'message', "<div class='alert alert-success'>" . $this->lang->line( 'account_registered_email_sent' ) . " </div>" );
				} else {
					$this->session->set_flashdata( 'message', "<div class='alert alert-success'>" . $this->lang->line( 'account_registered' ) . " </div>" );
				}
			} else {
				$this->session->set_flashdata( 'message', "<div class='alert alert-danger'>" . $this->lang->line( 'error_to_add_data' ) . " </div>" );
			}
			redirect( 'login/registration/' );
		}
	}

	function verify_result( $rid ) {
		$this->load->helper( 'url' );
		$this->load->model( "result_model" );

		$data['result'] = $this->result_model->get_result( $rid );
		if ( $data['result']['gen_certificate'] == '0' ) {
			exit();
		}

		$certificate_text = $data['result']['certificate_text'];
		$certificate_text = str_replace( '{email}', $data['result']['email'], $certificate_text );
		$certificate_text = str_replace( '{name}', $data['result']['name'], $certificate_text );
		$certificate_text = str_replace( '{percentage_obtained}', $data['result']['percentage_obtained'], $certificate_text );
		$certificate_text = str_replace( '{score_obtained}', $data['result']['score_obtained'], $certificate_text );
		$certificate_text = str_replace( '{quiz_name}', $data['result']['quiz_name'], $certificate_text );
		$certificate_text = str_replace( '{status}', $data['result']['result_status'], $certificate_text );
		$certificate_text = str_replace( '{result_id}', $data['result']['rid'], $certificate_text );
		$certificate_text = str_replace( '{generated_date}', date( 'Y-m-d', $data['result']['end_time'] ), $certificate_text );

		$data['certificate_text'] = $certificate_text;
		$this->load->view( 'view_certificate_2', $data );
	}

	function authentication( $user, $pass ) {
		global $wp, $wp_rewrite, $wp_the_query, $wp_query;

		if ( empty( $user ) || empty( $pass ) ) {
			return false;
		} else {
			require_once( $this->config->item( 'wp-path' ) );
			$status = false;
			$auth = wp_authenticate( $user, $pass );
			if ( is_wp_error( $auth ) ) {
				$status = false;
			} else {

				// if username already exist in savsoft_users
				$this->db->where( 'wp_user', $user );
				$query = $this->db->get( 'savsoft_users' );
				if ( $query->num_rows() == 0 ) {
					$userdata = array(
						'password' => md5( $pass ),
						'wp_user' => $user,
						'su' => 0,
						'gid' => $this->config->item( 'default_group' )

					);
					$this->db->insert( 'savsoft_users', $userdata );
				}


				$status = true;
			}
			return $status;
		}
	}

	public function commercial() {
		$this->load->helper( 'url' );

		$data['page_title'] = $this->lang->line( 'no_page_found' );
		$data['img'] = '404.svg';
		$data['show_only_page'] = true;
		$this->load->view( 'header_new', $data );
		$this->load->view( 'files_missing', $data );
		$this->load->view( 'footer_new', $data );
	}

	// super admin code login controller
	public function superadminlogin() {
		$this->load->helper( 'url' );
		$logged_in = $this->session->userdata( 'logged_in_super_admin' );
		if ( $logged_in['su'] != '3' ) {
			exit( 'permission denied' );
		}

		$user = $this->user_model->admin_login();
		$user['base_url'] = base_url();
		$user['super'] = 3;
		$this->session->set_userdata( 'logged_in', $user );
		redirect( 'dashboard' );
	}

	public function ajax_check_login() {
		if ( $this->session->userdata( 'logged_in' ) ) {
			echo 1;
		} else {
			echo 0;
		}
	}

	public function getAccountGroupCreateApi() {
		print_r( json_encode( LIST_REGISTER_DOMAIN ) );
	}

	public function verify_app_login() {
		$data = $_POST['data'];
		$user = json_decode( $data );
		$status = $this->user_model->verify_app_login( $user->username, $user->password );
		print_r( json_encode( $status ) );
	}

	public function forgot_password() {
		$email = $this->input->post('email');
	
		$this->load->model('User_model');
		$user = $this->User_model->get_by_email($email);
	
		if (!$user) {
			echo json_encode(['status' => false, 'message' => 'Email không tồn tại']);
			return;
		}
	
		try {
			$token = bin2hex(random_bytes(32));
		} catch (Exception $e) {
			log_message('error', 'Token generation failed: ' . $e->getMessage());
			echo json_encode(['status' => false, 'message' => 'Không thể tạo token.']);
			return;
		}
	
		// Cập nhật token trong DB
		$this->db->where('email', $email);
		$this->db->update('savsoft_users', [
			'pw_reset_token' => $token,
			'last_reset_password_datetime' => date('Y-m-d H:i:s')
		]);
	
		$reset_link = site_url('login/reset_password?token=' . $token);
	
		$language = $user->language ?? 'english';

		if (SITE_ID == 2) {
			$language = 'english';
		}

		if ($language === 'vietnamese') {
			$subject = 'Khôi phục mật khẩu';
			$email_body = "
				<p>Xin chào,</p>
				<p>Bạn đã yêu cầu khôi phục mật khẩu. Vui lòng nhấp vào liên kết dưới đây để tiếp tục:</p>
				<p><a href='" . htmlspecialchars($reset_link, ENT_QUOTES, 'UTF-8') . "'>$reset_link</a></p>
				<p>Liên kết sẽ hết hạn sau 1 giờ. Nếu bạn không yêu cầu, vui lòng bỏ qua email này.</p>
				<p>Trân trọng,<br>Đội ngũ ScrumPass</p>
				<p style='font-size: 12px; color: #888;'>Nếu bạn không muốn nhận email nữa, <a href='#'>hủy đăng ký tại đây</a>.</p>
			";
		} else {
			$subject = 'Password Recovery';
			$email_body = "
				<p>Hello,</p>
				<p>You have requested to reset your password. Please click the link below to proceed:</p>
				<p><a href='" . htmlspecialchars($reset_link, ENT_QUOTES, 'UTF-8') . "'>$reset_link</a></p>
				<p>The link will expire in 1 hour. If you did not request this, please ignore this email.</p>
				<p>Thank you,<br>ScrumPass Team</p>
				<p style='font-size: 12px; color: #888;'>If you no longer wish to receive emails from us, <a href='#'>unsubscribe here</a>.</p>
			";
		}
	
		$this->load->library('email');
	
		$config = [
			'protocol'    => 'smtp',
			'smtp_host'   => 'ssl://mail.scrumpass.com',
			'smtp_port'   => 465,
			'smtp_user'   => '<EMAIL>',
			'smtp_pass'   => 'ScrumPass2021',
			'mailtype'    => 'html',
			'charset'     => 'utf-8',
			'wordwrap'    => true,
			'newline'     => "\r\n",
			'crlf'        => "\r\n"
		];
	
		$this->email->initialize($config);
		$this->email->from('<EMAIL>', 'ScrumPass');
		$this->email->to($email);
		$this->email->reply_to('<EMAIL>', 'ScrumPass Support');
		$this->email->subject($subject);
		$this->email->message($email_body);
	
		if ($this->email->send()) {
			echo json_encode(['status' => true, 'message' => 'Email đặt lại mật khẩu đã được gửi.']);
		} else {
			$email_error_details = $this->email->print_debugger(['headers']);
			echo json_encode([
				'status' => false,
				'message' => 'Gửi email thất bại. Vui lòng thử lại sau.',
				'error_details' => $email_error_details
			]);
		}
	}	

	public function reset_password() {
		$token = $this->input->get( 'token' );
		if ( ! $token ) {
			show_error( 'Token không hợp lệ.' );
			return;
		}

		// Tìm user theo token
		$user = $this->db->get_where( 'savsoft_users', [ 'pw_reset_token' => $token ] )->row();

		if ( ! $user ) {
			show_error( 'Token không tồn tại hoặc đã hết hạn.' );
			return;
		}

		if ( isset( $user->last_reset_password_datetime ) && strtotime( $user->last_reset_password_datetime ) < strtotime( '-1 hour' ) ) {
			show_error( 'Token đã hết hạn.' );
			return;
		}
		$this->load->view( 'reset_password_form', [ 'token' => $token ] );
	}
    public function do_reset_password() { 
        $token = $this->input->post('token');
        $password = $this->input->post('password');
    
        // Retrieve user based on token
        $user = $this->db->get_where('savsoft_users', ['pw_reset_token' => $token])->row();
    
        if (!$user) {
            echo 'Token không hợp lệ.';
            return;
        }
    
        $hashed_password = md5($password);

        if (!empty($user->firebase_id)) {
            try {
                $this->auth->changeUserPassword($user->firebase_id, $password);
            } catch (\Throwable $th) {
                log_message('error', 'Firebase password reset failed: ' . $th->getMessage());
            }
        }

        $this->db->where('uid', $user->uid);
        $this->db->update('savsoft_users', [
            'password' => $hashed_password,
            'pw_reset_token' => null,
            'last_reset_password_datetime' => null
        ]);
    
        echo 'Mật khẩu đã được thay đổi thành công.';
    }
}