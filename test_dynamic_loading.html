<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Loading Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .tab { padding: 10px 20px; margin: 5px; background: #f0f0f0; cursor: pointer; }
        .tab.active { background: #007cba; color: white; }
        .content { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .loading { text-align: center; padding: 40px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .nav-btn { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .nav-btn:disabled { background: #ccc; cursor: not-allowed; }
        .search-box { width: 100%; padding: 10px; margin: 10px 0; }
        .question-counter { text-align: center; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dynamic Question Loading Test</h1>
        
        <!-- Search -->
        <input type="text" id="inputSearch" class="search-box" placeholder="Search questions...">
        
        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active" onclick="change_tab('wrong')">Wrong Questions <span id="wrong_count">(0)</span></div>
            <div class="tab" onclick="change_tab('bookmark')">Bookmarked <span id="bookmark_count">(0)</span></div>
            <div class="tab" onclick="change_tab('note')">Notes <span id="note_count">(0)</span></div>
        </div>
        
        <!-- Show All Toggle -->
        <label>
            <input type="checkbox" id="show_all_checkbox"> Show All Questions
        </label>
        
        <!-- Content Areas -->
        <div id="wrongQuestion" class="content">Loading...</div>
        <div id="allQuestion" class="content" style="display:none;">Loading...</div>
        <div id="noteQuestion" class="content" style="display:none;">Loading...</div>
        
        <!-- Navigation -->
        <div class="question-counter" id="count_item">
            <span id="current-item">1</span>/<span id="total-item">0</span>
        </div>
        
        <div style="text-align: center;">
            <button class="nav-btn previous-question" onclick="previous_ques()">Previous</button>
            <button class="nav-btn next-question" onclick="next_ques()">Next</button>
        </div>
    </div>

    <script>
        // Test variables - simulating the actual implementation
        var current_tab = 'wrong';
        var show_all = false;
        var inputSearch = document.getElementById("inputSearch");
        var current_questions = [];
        var total_questions = 0;
        var isLoading = false;
        var searchTimeoutId;
        var listTranslate = [];
        var current_index = 0;

        // Mock data for testing
        var mockData = {
            wrong: [
                {qid: 1, question: "What is 2+2?", options: [], correct: "4", userAns: "3"},
                {qid: 2, question: "What is the capital of France?", options: [], correct: "Paris", userAns: "London"},
                {qid: 3, question: "What is JavaScript?", options: [], correct: "Programming language", userAns: "Database"}
            ],
            bookmark: [
                {qid: 4, question: "Bookmarked question 1", options: [], correct: "Answer 1", userAns: "Answer 1"},
                {qid: 5, question: "Bookmarked question 2", options: [], correct: "Answer 2", userAns: "Answer 2"}
            ],
            note: [
                {qid: 6, question: "Note question 1", options: [], correct: "Note answer 1", userAns: ""},
                {qid: 7, question: "Note question 2", options: [], correct: "Note answer 2", userAns: ""}
            ]
        };

        function showLoading() {
            isLoading = true;
            var activeTab = current_tab == 'wrong' ? '#wrongQuestion' : 
                            current_tab == 'note' ? '#noteQuestion' : '#allQuestion';
            $(activeTab).html('<div class="loading"><div class="spinner"></div><p>Loading questions...</p></div>');
        }

        function hideLoading() {
            isLoading = false;
        }

        function change_tab(tab) {
            if(isLoading) return;
            
            // Update tab appearance
            $('.tab').removeClass('active');
            $('[onclick="change_tab(\'' + tab + '\')"]').addClass('active');
            
            // Hide all content areas
            $('.content').hide();
            
            listTranslate = [];
            inputSearch.value = "";
            $('#show_all_checkbox').prop('checked', false);
            current_tab = tab;
            current_index = 0;
            show_all = false;

            // Show active content area
            var activeTab = current_tab == 'wrong' ? '#wrongQuestion' : 
                            current_tab == 'note' ? '#noteQuestion' : '#allQuestion';
            $(activeTab).show();

            loadQuestions();
        }

        function loadQuestions() {
            if(isLoading) return;
            
            showLoading();
            
            var searchValue = inputSearch.value.trim();
            var index = show_all ? -1 : current_index;
            
            // Simulate API delay
            setTimeout(function() {
                var data = mockData[current_tab] || [];
                
                // Apply search filter
                if(searchValue) {
                    data = data.filter(q => q.question.toLowerCase().includes(searchValue.toLowerCase()));
                }
                
                hideLoading();
                
                if(index === -1) {
                    // Show all questions
                    current_questions = data;
                    total_questions = data.length;
                } else {
                    // Show single question
                    current_questions = data[index] ? [data[index]] : [];
                    total_questions = data.length;
                }
                
                updateUI();
                displayQuestions();
            }, 500);
        }

        function displayQuestions() {
            var activeTab = current_tab == 'wrong' ? '#wrongQuestion' : 
                            current_tab == 'note' ? '#noteQuestion' : '#allQuestion';
            
            if(current_questions.length === 0) {
                $(activeTab).html('<div class="loading"><p>No questions found</p></div>');
                return;
            }
            
            var html = '';
            current_questions.forEach(function(q, index) {
                html += '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">';
                html += '<h3>Question ' + (show_all ? index + 1 : current_index + 1) + '</h3>';
                html += '<p><strong>Q:</strong> ' + q.question + '</p>';
                html += '<p><strong>Correct:</strong> ' + q.correct + '</p>';
                html += '<p><strong>Your Answer:</strong> ' + (q.userAns || 'Not answered') + '</p>';
                html += '</div>';
            });
            
            $(activeTab).html(html);
        }

        function updateUI() {
            // Update question counters
            var countElement = current_tab == 'wrong' ? '#wrong_count' : 
                               current_tab == 'note' ? '#note_count' : '#bookmark_count';
            $(countElement).text('(' + total_questions + ')');

            // Update navigation
            if(show_all) {
                $('.previous-question, .next-question').hide();
                $('#count_item').hide();
            } else {
                $('.previous-question, .next-question').show();
                $('#count_item').show();
                
                $('#current-item').text(total_questions > 0 ? current_index + 1 : 0);
                $('#total-item').text(total_questions);
                
                $('.previous-question').prop('disabled', current_index <= 0);
                $('.next-question').prop('disabled', current_index >= total_questions - 1);
            }
        }

        function next_ques() {
            if(isLoading || show_all || current_index >= total_questions - 1) return;
            current_index++;
            loadQuestions();
        }

        function previous_ques() {
            if(isLoading || show_all || current_index <= 0) return;
            current_index--;
            loadQuestions();
        }

        function performSearch() {
            if(isLoading) return;
            show_all = false;
            $('#show_all_checkbox').prop('checked', false);
            current_index = 0;
            loadQuestions();
        }

        // Event listeners
        $('#show_all_checkbox').change(function() {
            if(isLoading) return;
            show_all = $(this).is(':checked');
            current_index = show_all ? -1 : 0;
            loadQuestions();
        });

        inputSearch.addEventListener("keydown", function(event) {
            clearTimeout(searchTimeoutId);
            if (event.keyCode === 13) {
                performSearch();
            } else {
                searchTimeoutId = setTimeout(performSearch, 1500);
            }
        });

        // Initialize
        $(document).ready(function() {
            loadQuestions();
        });
    </script>
</body>
</html>
