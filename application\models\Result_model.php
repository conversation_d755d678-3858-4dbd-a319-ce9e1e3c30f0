<?php
class Result_model extends CI_Model
{
    public function result_list($limit = '0', $status = '0', $dashboard = false)
    {
        $result_open = $this->lang->line('open');
        $logged_in = $this->session->userdata('logged_in');
        $userData = $this->user_model->get_user($logged_in['uid']);
        if ($logged_in["account_name"] == "Teacher") {
            $gid = $userData['gid'];
            $uid = "0";
        } else if($logged_in['account_name'] == "Administrator") {
            $gid = $this->input->get('gid');
            $uid = $this->input->get('uid');
        } else {
            $uid = $userData['uid'];
            $gid = $userData['gid'];
        }
        $userCategory = $this->db->where("FIND_IN_SET('".$userData['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
        if ($this->input->post('search')) {
            $search = $this->input->post('search');
            $this->db->group_start();
            $this->db->or_where('savsoft_users.email', $search);
            $this->db->or_where('savsoft_users.name', $search);
            $this->db->or_where('savsoft_users.contact_no', $search);
            $this->db->or_where('savsoft_result.rid', $search);
            $this->db->or_where('savsoft_quiz.quiz_name', $search);
            $this->db->group_end();
        } else if ($this->input->get('uid') || $this->input->get('quid') || $this->input->get('gid') || $this->input->get('status')) {
            $quid = $this->input->get('quid');
            $status = $this->input->get('status');
            $search_uid = $this->input->get('uid');
            if ($search_uid != "0" && $search_uid) {
                $uid = $search_uid;
            }
            if($search_uid) {
				$this->db->group_start();
				foreach($uid as $val) {
					$this->db->or_where('savsoft_result.uid', $val);
				}
				$this->db->group_end();
			}else{
                if($userData['su'] != 1)
                $this->db->where('savsoft_result.uid', $uid);
            }
            if($quid) {
				$this->db->group_start();
				foreach($quid as $val) {
					$this->db->or_where('savsoft_result.quid', $val);
				}
				$this->db->group_end();
			}
            if($gid) {
				$this->db->group_start();
				/* foreach($gid as $val) {
					$this->db->or_where(" FIND_IN_SET('" . $val . "', gids)");
				} */
                foreach($gid as $val) {
                    $this->db->or_where('savsoft_users.gid', $val);
				}
				$this->db->group_end();
			}
            if ($status != "0") {
                $this->db->where("savsoft_result.result_status", $status);
            }

        } else {
            $this->db->where('savsoft_result.result_status !=', $result_open);
        }
        if (!in_array('List_all', explode(',', $logged_in['results']))&&$logged_in['account_name']!="Teacher") {
            $this->db->where('savsoft_result.uid', $uid);
            $this->db->group_start();
            $this->db->where(" FIND_IN_SET('" . $gid . "', savsoft_quiz.gids)");
            $this->db->or_where("savsoft_result.categories", $userCategory['category_name']);
            $this->db->group_end();
        } else {
            if ($logged_in["account_name"] == "Teacher") {
                if ($gid != "0") {
                    $this->db->where('savsoft_users.gid', $gid);
                }
            }
        }
        if ($status != '0') {
            $this->db->where('savsoft_result.result_status', $status);
        }
        if($userData['su'] != 1)
        //$this->db->limit($this->config->item('number_of_rows'), $limit);
        if($dashboard)
        $this->db->limit($limit);
        $this->db->order_by('rid', 'desc');
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }

    public function result_list_admin_ajax($limit = '0', $offset = '0', $search){
        $gid = $this->input->get('gid');
        $uid = $this->input->get('uid');
        if ($search != "" && $search != null) {
            $this->db->or_like('savsoft_users.email', $search);
            $this->db->or_where('savsoft_users.name', $search);
            $this->db->or_where('savsoft_result.app_username', $search);
            $this->db->or_like('savsoft_result.rid', $search);
            $this->db->or_where('savsoft_quiz.quiz_name', $search);
            $this->db->or_where('savsoft_result.debug_id', $search);
        }
        if ($this->input->get('uid') || $this->input->get('quid') || $this->input->get('gid') || $this->input->get('status')) {
            $quid = $this->input->get('quid');
            $status = $this->input->get('status');
            $search_uid = $this->input->get('uid');
            if ($search_uid != "0" && $search_uid) {
                $uid = $search_uid;
            }
            if($search_uid) {
                $this->db->group_start();
                foreach($uid as $val) {
                    $this->db->or_where('savsoft_result.uid', $val);
                }
                $this->db->group_end();
            }
            if($quid) {
                $this->db->group_start();
                foreach($quid as $val) {
                    $this->db->or_where('savsoft_result.quid', $val);
                }
                $this->db->group_end();
            }
            if($gid) {
                $this->db->group_start();
                /* foreach($gid as $val) {
                    $this->db->or_where(" FIND_IN_SET('" . $val . "', gids)");
                } */
                foreach($gid as $val) {
                    $this->db->or_where('savsoft_users.gid', $val);
                }
                $this->db->group_end();
            }
            if ($status != "0" && $status != null) {
                $this->db->where("savsoft_result.result_status", $status);
            }
        }
        
        $this->db->limit($limit);
        $this->db->offset($offset);
        $this->db->order_by('rid', 'desc');
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }

    public function result_list_ajax($limit = '0', $offset = '0', $search)
    {
        $result_open = $this->lang->line('open');
        $logged_in = $this->session->userdata('logged_in');
        $userData = $this->user_model->get_user($logged_in['uid']);
        $userCategory = $this->db->where("FIND_IN_SET('".$userData['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
        if ($userData['account_name'] == "Teacher") {
            $gid = $userData['gid'];
            $uid = "0";
            if ($gid != "0") {
                $this->db->where('savsoft_users.gid', $gid);
            }
        } else if($userData['account_name'] == "Administrator") {
            $gid = $this->input->get('gid');
            $uid = $this->input->get('uid');
        } else {
            $uid = $userData['uid'];
            $gid = $userData['gid'];
        }
        if ($search != "" && $search != null) {
            $this->db->or_like('savsoft_users.email', $search);
            $this->db->or_where('savsoft_users.name', $search);
            $this->db->or_where('savsoft_result.app_username', $search);
            $this->db->or_like('savsoft_result.rid', $search);
            $this->db->or_where('savsoft_quiz.quiz_name', $search);
            $this->db->or_where('savsoft_result.debug_id', $search);
        }
        if ($this->input->get('uid') || $this->input->get('quid') || $this->input->get('gid') || $this->input->get('status')) {
            $quid = $this->input->get('quid');
            $status = $this->input->get('status');
            $search_uid = $this->input->get('uid');
            if ($search_uid != "0" && $search_uid) {
                $uid = $search_uid;
            }
            if($search_uid) {
                $this->db->group_start();
                foreach($uid as $val) {
                    $this->db->or_where('savsoft_result.uid', $val);
                }
                $this->db->group_end();
            }else{
                if($userData['su'] != 1)
                $this->db->where('savsoft_result.uid', $uid);
            }
            if($quid) {
                $this->db->group_start();
                foreach($quid as $val) {
                    $this->db->or_where('savsoft_result.quid', $val);
                }
                $this->db->group_end();
            }
            if($gid) {
                $this->db->group_start();
                /* foreach($gid as $val) {
                    $this->db->or_where(" FIND_IN_SET('" . $val . "', gids)");
                } */
                foreach($gid as $val) {
                    $this->db->or_where('savsoft_users.gid', $val);
                }
                $this->db->group_end();
            }
            if ($status != "0") {
                $this->db->where("savsoft_result.result_status", $status);
            }
        }else{
            $this->db->where('savsoft_result.result_status !=', $result_open);
        }
        if (!in_array('List_all', explode(',', $logged_in['results']))&&$logged_in['account_name']!="Teacher") {
            $this->db->where('savsoft_result.uid', $uid);
            $this->db->group_start();
            $this->db->where(" FIND_IN_SET('" . $gid . "', savsoft_quiz.gids)");
            $this->db->or_where("savsoft_result.categories", $userCategory['category_name']);
            $this->db->group_end();
        } else {
            if ($logged_in["account_name"] == "Teacher") {
                if ($gid != "0") {
                    $this->db->where('savsoft_users.gid', $gid);
                }
            }
        }
        $this->db->limit($limit);
        $this->db->offset($offset);
        $this->db->order_by('rid', 'desc');
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }
    public function count_results($search)
    {
        $result_open = $this->lang->line('open');
        $logged_in = $this->session->userdata('logged_in');
        if ($logged_in['account_name'] == "Teacher") {
            $gid = $logged_in['gid'];
            $uid = "0";
            if ($gid != "0") {
                $this->db->where('savsoft_users.gid', $gid);
            }
            $this->db->where('savsoft_result.result_status !=', $result_open);
        } else if($logged_in['account_name'] == "Administrator") {
            $gid = $this->input->get('gid');
            $uid = $this->input->get('uid');
        } else {
            $uid = $logged_in['uid'];
            $gid = "0";
        }
        if ($search != "" && $search != null) {
            $this->db->or_like('savsoft_users.email', $search);
            $this->db->or_where('savsoft_users.name', $search);
            $this->db->or_where('savsoft_result.app_username', $search);
            $this->db->or_like('savsoft_result.rid', $search);
            $this->db->or_where('savsoft_quiz.quiz_name', $search);
            $this->db->or_where('savsoft_result.debug_id', $search);
        } 
        if ($this->input->get('uid') || $this->input->get('quid') || $this->input->get('gid') || $this->input->get('status')) {
            $quid = $this->input->get('quid');
            $status = $this->input->get('status');
            $search_uid = $this->input->get('uid');
            if ($search_uid != "0" && $search_uid) {
                $uid = $search_uid;
            }
            if($search_uid) {
                $this->db->group_start();
                foreach($uid as $val) {
                    $this->db->or_where('savsoft_result.uid', $val);
                }
                $this->db->group_end();
            }else{
                if($logged_in['su'] != 1)
                $this->db->where('savsoft_result.uid', $uid);
            }
            if($quid) {
                $this->db->group_start();
                foreach($quid as $val) {
                    $this->db->or_where('savsoft_result.quid', $val);
                }
                $this->db->group_end();
            }
            if($gid) {
                $this->db->group_start();
                /* foreach($gid as $val) {
                    $this->db->or_where(" FIND_IN_SET('" . $val . "', gids)");
                } */
                foreach($gid as $val) {
                    $this->db->or_where('savsoft_users.gid', $val);
                }
                $this->db->group_end();
            }
            if ($status != "0") {
                $this->db->where("savsoft_result.result_status", $status);
            }
        }else{
            $this->db->where('savsoft_result.result_status !=', $result_open);
        }
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->num_rows();
    }


    public function quiz_list()
    {
        $logged_in = $this->session->userdata('logged_in');
        $userCategory = $this->db->where("FIND_IN_SET('".$logged_in['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
        $category = $userCategory['category_name'];
        $uid = $logged_in['uid'];
        if ($logged_in['account_name'] == "Administrator") {
            $this->db->order_by('quid', 'desc');
            $query = $this->db->get('savsoft_quiz');
            return $query->result_array();
        } else if($logged_in['account_name'] == "Teacher"){
            $query = $this->db->query("select savsoft_quiz.quid,savsoft_quiz.quiz_name from savsoft_result INNER JOIN savsoft_quiz ON savsoft_result.quid = savsoft_quiz.quid inner join savsoft_users on savsoft_users.uid = savsoft_result.uid where savsoft_result.uid= '$uid' or savsoft_users.gid = '".$logged_in['gid']."' GROUP by quiz_name");
            return $query->result_array();
        }else{
            $query = $this->db->query("select savsoft_quiz.quid, CASE 
            WHEN savsoft_quiz.quiz_name IS NULL THEN savsoft_result.practice_name 
            ELSE savsoft_quiz.quiz_name 
            END AS quiz_name from savsoft_result LEFT JOIN savsoft_quiz ON savsoft_result.quid = savsoft_quiz.quid where uid= '$uid' AND savsoft_result.categories = '$category' GROUP by quiz_name");
            $data = $query->result_array();
            return $data;
        }
    }
    public function no_attempt($quid, $uid, $rid = 0)
    {
        $query = $this->db->query("select * from savsoft_result where uid='$uid' and quid='$quid' ");
        if($rid != 0){
            $i = 1;
            foreach($query->result_array() as $key => $value){
                if($value['rid'] == $rid){
                    break;
                }
                $i++;
            }
            return $i;
        }else{
            return $query->num_rows();   
        }
    }
    public function remove_result($rid)
    {
        $this->db->where('savsoft_result.rid', $rid);
        if ($this->db->delete('savsoft_result')) {
            $this->db->where('rid', $rid);
            $this->db->delete('savsoft_answers');
            return true;
        } else {
            return false;
        }
    }
    public function generate_report($quid, $gid)
    {
        $logged_in = $this->session->userdata('logged_in');
        $uid = $logged_in['uid'];
        $date1 = $this->input->post('date1');
        $date2 = $this->input->post('date2');
        if ($quid != '0') {
            $this->db->where('savsoft_result.quid', $quid);
        }
        if ($gid != '0') {
            $this->db->where('savsoft_users.gid', $gid);
        }
        if ($date1 != '') {
            $this->db->where('savsoft_result.start_time >=', strtotime($date1));
        }
        if ($date2 != '') {
            $this->db->where('savsoft_result.start_time <=', strtotime($date2));
        }
        $this->db->order_by('rid', 'desc');
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_group', 'savsoft_group.gid=savsoft_users.gid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }
    public function get_result($rid)
    {
        $logged_in = $this->session->userdata('logged_in');
        $uid = $logged_in['uid'];
        if ($logged_in['su'] == '0') {
            $this->db->where('savsoft_result.uid', $uid);
        }
        $this->db->where('savsoft_result.rid', $rid);
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_group', 'savsoft_group.gid=savsoft_users.gid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->row_array();
    }
    public function last_ten_result($quid)
    {
        $this->db->order_by('end_time', 'desc');
        $this->db->limit(10);
        $this->db->where('savsoft_result.quid', $quid);
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }
    public function get_percentile($quid, $uid, $score)
    {
        $logged_in = $this->session->userdata('logged_in');
        $gid = $logged_in['gid'];
        $res = array();
        $this->db->select("savsoft_result.uid");
        $this->db->where("savsoft_result.quid", $quid);
        $this->db->group_by("savsoft_result.uid");
        // $this->db->order_by("savsoft_result.score_obtained",'DESC');
        $query = $this->db->get('savsoft_result');
        $res[0] = $query->num_rows();
        $this->db->select("savsoft_result.uid");
        $this->db->where("savsoft_result.quid", $quid);
        $this->db->where("savsoft_result.uid !=", $uid);
        $this->db->where("savsoft_result.score_obtained <=", $score);
        $this->db->group_by("savsoft_result.uid");
        // $this->db->order_by("savsoft_result.score_obtained",'DESC');
        $querys = $this->db->get('savsoft_result');
        $res[1] = $querys->num_rows();
        return $res;
    }
    public function question_months()
    {
        $revenue = array();
        $months = array('Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec');
        foreach ($months as $k => $val) {
            $p1 = strtotime(date('Y', time()) . '-' . $val . '-01');
            $p2 = strtotime(date('Y', time()) . '-' . $val . '-' . date('t', $p1));
            //$query = $this->db->query("select * from savsoft_payment where paid_date >='$p1' and paid_date <='$p2'   ");
            $query = $this->db->query("select rid from savsoft_result where end_time >='$p1' and end_time <='$p2'   ");
            // $rev = $query->result_array();
            if ($query->num_rows() == 0) {
                $revenue[$val] = 0;
            } else {
                $revenue[$val] = $query->num_rows();
            }
        }
        /* $p1 = strtotime(date('Y', time()) . '-' . 'Jul' . '-01');
        $p2 = strtotime(date('Y', time()) . '-' . 'Aug' . '-' . date('t', $p1));
        $query = $this->db->query("select * from savsoft_result where end_time >='$p1' and end_time <='$p2'   ");
        $rev = $query->result_array(); */
        return $revenue;
    }
    public function question_months_statistic()
    {
		$now = time();
		$past = time()- 15768000;
		$arr = array();
		$months = [];
        $this->db->where('end_time >=', $past);
        $this->db->where('end_time <=', $now);
        $this->db->order_by('end_time','desc');
        $this->db->select('end_time');
        $query = $this->db->get('savsoft_result');
		$query = $query->result_array();
		foreach($query as $key => $value){
				$arr[date('m/y', $value['end_time'])] ++;
		}
		$arr = array_reverse($arr);
		return $arr;
    }
    public function save_tag($saved, $rid = 0)
    {
        if($rid == 0) {
            $rid = $this->session->userdata('rid');
        }
        $this->db->set('saved_r', '"' . $saved . '"', false);
        $this->db->where('rid', $rid);
        $this->db->update('savsoft_result');
    }
    public function result_percentage()
    {
        $result_open = $this->lang->line('open');
        $this->db->order_by('quid', 'desc');
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->select('percentage_obtained');
        $query = $this->db->get('savsoft_result');
        $timeDoQuiz = $query->num_rows();
        // User mới chưa làm bài nào
        if($timeDoQuiz == 0) return 0;

        $total = 0;
        foreach ($query->result_array() as $k => $val) {
            $total += $val['percentage_obtained'];
        }
        $result = $total / $timeDoQuiz;
        return round($result, 2);
    }
    public function user_result_percentage($uid, $gid,$category)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->group_start();
            $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
            $this->db->or_group_start();
                $this->db->where("FIND_IN_SET('" . $category . "', categories)");
                $this->db->where('savsoft_result.quid', 0);
                $this->db->where('uid', $uid);
            $this->db->group_end();
        $this->db->group_end();
        //lọc kết quả lớn hơn 120s và kết quả lớn hơn 10%
        $this->db->not_group_start();
            $this->db->where('savsoft_result.percentage_obtained <', 10); 
            $this->db->where('savsoft_result.total_time <', 120); 
        $this->db->group_end();
        //end
        $this->db->group_start();
            $this->db->where('savsoft_result.result_status !=', "Open");
            $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->select('percentage_obtained');
        $this->db->select('total_time');
        $query = $this->db->get('savsoft_result');
        $timeDoQuiz = $query->num_rows();
        // User mới chưa làm bài nào
        if($timeDoQuiz == 0) return 0;

        $total = 0;
        foreach ($query->result_array() as $k => $val) {
            $total += $val['percentage_obtained'];
        }
        $result = $total / $timeDoQuiz;
        return round($result, 2);
    }
    function prezero($val)
	{
		if ($val <= 9) {
			return '0' . $val;
		} else {
			return $val;
		}
	}
    function secintomin($sec)
	{
		if ($sec >= 60) {
			$splitin = explode('.', ($sec / 60));
			if (isset($splitin[1])) {
				$secs = substr(intval((substr($splitin[1], 0, 2) * 60) / 100), 0, 2);
			} else {
				$secs = 0;
			}
			return $splitin[0] . ':' . $this->prezero($secs);
		} else {
			return '0:' . $this->prezero($sec);
		}
	}
    public function user_result_percentage_exam($uid, $gid)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->where('savsoft_quiz.exam_quiz', 1);
        //lọc kết quả lớn hơn 120s và kết quả lớn hơn 10%
        $this->db->not_group_start();
            $this->db->where('savsoft_result.percentage_obtained <', 10); 
            $this->db->where('savsoft_result.total_time <', 120); 
        $this->db->group_end();
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        $total = 0;
        $count = 0;
        if($query->num_rows() != 0){
            foreach ($query->result_array() as $k => $val) {
                if($val["exam_quiz"] == 1){
                    $count++;
                    $total += $val['percentage_obtained'];
                }
            }
            if($count != 0){
                $result = $total / $count;
                if(in_array($gid, SCALE_GROUP_EXAM["PMP"])){
                    $result = $result * 1.3;
                }else if(in_array($gid, SCALE_GROUP_EXAM["ACP"])){
                    $result = $result * 1.3;
                }else if(in_array($gid, SCALE_GROUP_EXAM["CCBA"])){
                    $result = $result * 1.2;
                }else if(in_array($gid, SCALE_GROUP_EXAM["CBAP"])){
                    $result = $result * 1.3;
                }
            }else{
                $result = 0;
            }
        }else{
            $result = 0;
        }
        return round($result, 2);
    }
    public function user_result_percentage_recent($uid, $gid ,$category)
    {
        $result_open = "Open";
        $this->db->where('uid', $uid);
        $this->db->group_start();
            $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
            $this->db->or_group_start();
                $this->db->where("FIND_IN_SET('" . $category . "', categories)");
                $this->db->where('savsoft_result.quid', 0);
                $this->db->where('uid', $uid);
            $this->db->group_end();
        $this->db->group_end();
        //lọc kết quả lớn hơn 120s và kết quả lớn hơn 10%
        $this->db->not_group_start();
            $this->db->where('savsoft_result.percentage_obtained <', 10); 
            $this->db->where('savsoft_result.total_time <', 120); 
        $this->db->group_end();
        //end
        $this->db->group_start();
            $this->db->where('savsoft_result.result_status !=', $result_open);
            $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->select('percentage_obtained');
        $this->db->select('rid');
        $this->db->limit(10);
        $this->db->order_by('rid', 'desc');
        $query = $this->db->get('savsoft_result');
        $timeDoQuiz = $query->num_rows();
        // User mới chưa làm bài nào
        if($timeDoQuiz == 0) return 0;

        $total = 0;
        foreach ($query->result_array() as $k => $val) {
            $total += $val['percentage_obtained'];
        }
        $result = $total / $timeDoQuiz;
        return round($result, 2);
    }
    public function avalible_quiz($uid, $gid)
    {
        $where = "FIND_IN_SET('" . $gid . "', gids) or FIND_IN_SET('" . $uid . "', uids)";
        $this->db->select('*');
        $this->db->select('count(savsoft_result.quid) as result_count');
        $this->db->select('savsoft_quiz.quid as squid');
        $this->db->join('savsoft_result', 'savsoft_result.quid = savsoft_quiz.quid', 'left');
        $this->db->join('quiz_order','quiz_order.exam_id = savsoft_quiz.quid and quiz_order.group_id='.$gid, 'left');
        //$this->db->where('savsoft_result.uid',$uid);
        $this->db->where($where);
        $this->db->where('quiz_order.group_id', $gid);
        $this->db->where('savsoft_result.result_status !=', "Open");
        $this->db->group_by('savsoft_quiz.quid');
        //$this->db->order_by('result_count', 'desc');
        $this->db->order_by('order_number', 'asc');
        $query = $this->db->get('savsoft_quiz');
        //$query2 = $this->db->query("select *,count(savsoft_result.quid) as result_count,savsoft_quiz.quid as squid FROM `savsoft_quiz` LEFT JOIN savsoft_result on savsoft_quiz.quid = savsoft_result.quid WHERE FIND_IN_SET('8', gids) or FIND_IN_SET('76', uids) and savsoft_result.result_status != 'Open' GROUP BY savsoft_quiz.quid ORDER BY result_count DESC");
        return $query->result_array();
    }
    public function get_num_quiz_done($uid, $gid)
    {

        $where = "FIND_IN_SET('" . $gid . "', gids) or FIND_IN_SET('" . $uid . "', uids)";
        $this->db->select('savsoft_quiz.quid');
        $this->db->select('count(savsoft_result.quid) as result_count');
        $this->db->join('savsoft_result', 'savsoft_result.quid = savsoft_quiz.quid', 'left');
        //$this->db->where('savsoft_result.uid',$uid);
        $this->db->where('savsoft_result.result_status !=', "Open");
        $this->db->where($where);
        $this->db->group_by('savsoft_quiz.quid');
        //$this->db->order_by('result_count', 'desc');
        $query = $this->db->get('savsoft_quiz');
        return $query->result_array();
    }
    public function user_quiz_detail_fail($uid, $gid)
    {
        //$where = "FIND_IN_SET('" . $uid . "', uids)";
        $this->db->select('savsoft_quiz.quid');
        $this->db->select('count(savsoft_result.quid) as result_count');
        $this->db->join('savsoft_result', 'savsoft_result.quid = savsoft_quiz.quid', 'left');
        $this->db->where('savsoft_result.result_status', "Fail");
        $this->db->where('savsoft_result.uid', $uid);
        //$this->db->where($where);
        $this->db->group_by('savsoft_quiz.quid');
        //$this->db->order_by('end_date', 'asc');
        $query = $this->db->get('savsoft_quiz');
        return $query->result_array();
    }
    public function user_quiz_detail_pass($uid, $gid)
    {
        //$where = "FIND_IN_SET('" . $uid . "', uids)";
        $this->db->select('savsoft_quiz.quid');
        $this->db->select('count(savsoft_result.quid) as result_count');
        $this->db->join('savsoft_result', 'savsoft_result.quid = savsoft_quiz.quid', 'left');
        $this->db->where('savsoft_result.result_status', "Pass");
        //$this->db->where($where);
        $this->db->where('savsoft_result.uid', $uid);
        $this->db->group_by('savsoft_quiz.quid');
        //$this->db->order_by('end_date', 'asc');
        $query = $this->db->get('savsoft_quiz');
        return $query->result_array();
    }
    public function win_chain($uid, $gid, $category)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->or_group_start();
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('savsoft_result.quid', 0);
        $this->db->where('uid', $uid);
        $this->db->group_end();
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->order_by('rid', 'desc');
        $query = $this->db->get('savsoft_result');
        $count = 0;
        $high_score = 0;
        $check = 0;
        foreach ($query->result_array() as $qr) {
            if ($qr['result_status'] == 'Pass') {
                $count++;
                if ($count > $high_score) {
                    $high_score = $count;
                }
                $check = 1;
            } else {
                $count = 0;
            }
        }
        if ($count == 0 && $check == 1) {
            $count = 1;
        }
        if ($high_score > $count) {
            return $high_score;
        } else {
            return $count;
        }
    }
    public function win_chain2($uid,$gid)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        //$this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        //$this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->order_by('rid', 'desc');
        $this->db->limit(10);
        $query = $this->db->get('savsoft_result');
        $count = 0;
        $high_score = 0;
        $check = 0;
        foreach ($query->result_array() as $qr) {
            if ($qr['result_status'] == 'Pass') {
                $count++;
                if ($count > $high_score) {
                    $high_score = $count;
                }
                $check = 1;
            } else {
                $count = 0;
            }
        }
        if ($count == 0 && $check == 1) {
            $count = 1;
        }
        if ($high_score > $count) {
            return $high_score;
        } else {
            return $count;
        }
    }
    public function win_chain3($uid,$gid)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        //$this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        //$this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->order_by('rid', 'desc');
        $this->db->limit(10);
        $query = $this->db->get('savsoft_result');
        $count = 0;
        $high_score = 0;
        $check = 0;
        foreach ($query->result_array() as $qr) {
            if ($qr['result_status'] == 'Pass') {
                $count++;
            } 
        }
        return $count;
        
    }
    public function total_time($uid,$gid)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->order_by('rid', 'desc');
        $query = $this->db->get('savsoft_result');
        $count = 0;
        foreach ($query->result_array() as $qr) {
            $count += $qr['total_time'];
        }
        return $count;
    }
    public function total_time_count($uid, $gid, $category)
    {
        $result_open = 'Open';
        $this->db->where('uid', $uid);
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->or_group_start();
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('savsoft_result.quid', 0);
        $this->db->where('uid', $uid);
        $this->db->group_end();
        //lọc kết quả lớn hơn 120s và kết quả lớn hơn 10%
        $this->db->not_group_start();
            $this->db->where('savsoft_result.percentage_obtained <', 10); 
            $this->db->where('savsoft_result.total_time <', 120); 
        $this->db->group_end();
        //end
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $query = $this->db->get('savsoft_result');
        $count = 0;
        foreach ($query->result_array() as $qr) {
            $count += $qr['total_time'];
        }
        return $count;
    }
    public function time_avg_recent_score($uid, $gid, $category)
    {
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->or_group_start();
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('savsoft_result.quid', 0);
        $this->db->where('uid', $uid);
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->limit(10);
        $this->db->order_by('rid', 'desc');
        $query = $this->db->get('savsoft_result');
        $data = $query->result_array();
        $count = 0;
        foreach ($data as $qr) {
            $count += $qr['total_time'];
        }

        $sum = 0;
        foreach ($data as $item) {
            $scores = explode(',', $item['score_individual']);
            $sum += count($scores);
        }
        if($sum == 0){
            return 0;
        }
        $time_avg = $count / $sum;
        return $time_avg;
    }
    public function last_result($uid, $gid, $category)
    {
        $result_open = "Open";
        $this->db->where('uid', $uid);
        $this->db->group_start();
            $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
            $this->db->or_group_start();
                $this->db->where("FIND_IN_SET('" . $category . "', categories)");
                $this->db->where('uid', $uid);
            $this->db->group_end();
        $this->db->group_end();
        $this->db->group_start();
            $this->db->where('savsoft_result.result_status !=', $result_open);
            $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->group_end();
        //lọc kết quả lớn hơn 120s và kết quả lớn hơn 10%
        $this->db->not_group_start();
            $this->db->where('savsoft_result.percentage_obtained <', 10); 
            $this->db->where('savsoft_result.total_time <', 120); 
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->order_by('rid', 'desc');
        $this->db->limit(1);
        $query = $this->db->get('savsoft_result');
        foreach ($query->result_array() as $qr) {
            $result = $qr['percentage_obtained'];
        }
        return $result;
    }
    public function quiz_each_day($uid)
    {
        $p1 = time() - (60 * 60 * 24 * 30 * 12);
        $p2 = time();
        $result_open = $this->lang->line('open');
        $this->db->where('uid', $uid);
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->order_by('rid', 'asc');
        $this->db->where('end_time >=', $p1);
        $this->db->where('end_time <=', $p2);
        $this->db->select('count(*) as count');
        $this->db->select("FROM_UNIXTIME(end_time,'%Y-%m-%d') as time");
        $this->db->group_by("day(FROM_UNIXTIME(end_time,'%Y-%m-%d')) ,month(FROM_UNIXTIME(end_time,'%Y-%m-%d')),year(FROM_UNIXTIME(end_time,'%Y-%m-%d'))");
        $this->db->query("SET SESSION time_zone = '+7:00'");
        $this->db->query("SET GLOBAL sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
        $query = $this->db->get('savsoft_result');
        //$query=$this->db->query("select count(*) as count ,FROM_UNIXTIME(end_time,'%Y-%m-%d') as time FROM `savsoft_result` where uid = '$uid' and end_time >= '$p1' and end_time <= '$p2' group by day(FROM_UNIXTIME(end_time,'%Y-%m-%d')),month(FROM_UNIXTIME(end_time,'%Y-%m-%d')),year(FROM_UNIXTIME(end_time,'%Y-%m-%d')) order by end_time asc");
        return $query->result_array();
    }
    public function statitics($uid, $gid, $category)
    {
        $result_open = /* $this->lang->line('open') */'Open';
        $this->db->where('savsoft_result.uid', $uid);
        $this->db->where('savsoft_result.result_status !=', $result_open);
        $this->db->where('savsoft_result.result_status !=', "Cancel");
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->or_group_start();
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('savsoft_result.quid', 0);
        $this->db->where('savsoft_result.uid', $uid);
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $this->db->select('percentage_obtained');
        $this->db->select('score_individual');
        $this->db->order_by('rid', 'desc');
        $query = $this->db->get('savsoft_result');
        //$query=$this->db->query("select count(*) as count ,FROM_UNIXTIME(end_time,'%Y-%m-%d') as time FROM `savsoft_result` where uid = '$uid' and end_time >= '$p1' and end_time <= '$p2' group by day(FROM_UNIXTIME(end_time,'%Y-%m-%d')),month(FROM_UNIXTIME(end_time,'%Y-%m-%d')),year(FROM_UNIXTIME(end_time,'%Y-%m-%d')) order by end_time asc");
        return $query->result_array();
    }
    public function quiz_each_day2($uid)
    {
        $p1 = strtotime('-30 day');
        $p2 = strtotime("now");
        $this->db->query("SET SESSION time_zone = '+7:00'");
        $query = $this->db->query("select count(*) as count ,FROM_UNIXTIME(end_time,'%Y-%m-%d') as time FROM `savsoft_result` where uid = '$uid' and end_time >= '1626513476' and end_time <= '1631870276' group by day(FROM_UNIXTIME(end_time,'%Y-%m-%d')),month(FROM_UNIXTIME(end_time,'%Y-%m-%d')),year(FROM_UNIXTIME(end_time,'%Y-%m-%d')) order by end_time asc");
        return $query->result_array();
    }
    public function question_months_user($uid)
    {
        $revenue = array();
        $months = array('Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec');
        foreach ($months as $k => $val) {
            $p1 = strtotime(date('Y', time()) . '-' . $val . '-01');
            $p2 = strtotime(date('Y', time()) . '-' . $val . '-' . date('t', $p1));
            //$query = $this->db->query("select * from savsoft_payment where paid_date >='$p1' and paid_date <='$p2'   ");
            $query = $this->db->query("select COUNT(*) as num from savsoft_result where uid='$uid' and end_time >='$p1' and end_time <= '$p2'   ");
            $rev = $query->result_array();
            if ($query->num_rows() != 0) {
                $revenue[$val] = $query->result_array();
            }
        }
        /* $p1 = strtotime(date('Y', time()) . '-' . 'Jul' . '-01');
        $p2 = strtotime(date('Y', time()) . '-' . 'Aug' . '-' . date('t', $p1));
        $query = $this->db->query("select * from savsoft_result where end_time >='$p1' and end_time <='$p2'   ");
        $rev = $query->result_array(); */
        return $revenue;
    }
    public function num_result()
    {
        $logged_in = $this->session->userdata('logged_in');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid');
        if ($logged_in['account_name'] == "User") {
            $uid = $logged_in['uid'];
            $gid = "0";
        } else if ($logged_in['account_name'] == "Teacher") {
            $gid = $logged_in['gid'];
            $uid = "0";
            if ($gid != "0") {
                $this->db->where('savsoft_users.gid', $gid);
            }
        } else {
            $gid = $this->input->get('gid');
            $uid = $this->input->get('uid');
        }
        if ($this->input->get('uid') || $this->input->get('quid') || $this->input->get('gid') || $this->input->get('status')) {
            $quid = $this->input->get('quid');
            $status = $this->input->get('status');
            $search_uid = $this->input->get('uid');
            if ($search_uid != "0" && $search_uid) {
                $uid = $search_uid;
            }
            if($search_uid) {
				$this->db->group_start();
				foreach($uid as $val) {
					$this->db->or_where('savsoft_result.uid', $val);
				}
				$this->db->group_end();
			}else{
                $this->db->where('savsoft_result.uid', $uid);
            }
            if($quid) {
				$this->db->group_start();
				foreach($quid as $val) {
					$this->db->or_where('savsoft_result.quid', $val);
				}
				$this->db->group_end();
			}
            if($gid) {
				$this->db->group_start();
				foreach($gid as $val) {
					//$this->db->or_where(" FIND_IN_SET('" . $val . "', gids)");
                    $this->db->or_where('savsoft_users.gid', $val);
				}
				$this->db->group_end();
			}
            if ($status != "0") {
                $this->db->where("savsoft_result.result_status", $status);
            }

        }
        $this->db->join('savsoft_users', 'savsoft_users.uid=savsoft_result.uid');
        $query = $this->db->get('savsoft_result');
        return 50;
    }
    public function get_user_num_result($uid)
    {
        $this->db->where('uid', $uid)->select('rid');
        $query = $this->db->get('savsoft_result');
        return $query->num_rows();
    }
    public function get_user_avg_score($uid) {
        $this->db->where('uid', $uid)->select_avg('percentage_obtained');
        $query = $this->db->get('savsoft_result');
        return $query->result_array();
    }

    public function get_last_result($uid, $category)
    {
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('uid', $uid);
        $this->db->order_by('rid', 'desc');
        $this->db->limit(2);
        $data = $this->db->get('savsoft_result');
        return $data->result_array();
    }
    public function continue_quiz($uid)
    {
        //$uid = $logged_in['uid'];
        $this->db->where('uid', $uid);
        $this->db->where('result_status', 'Open');
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid=savsoft_result.quid', 'left');
        $this->db->select('savsoft_quiz.quiz_name, savsoft_quiz.duration, savsoft_result.*, savsoft_quiz.noq');
        $query = $this->db->get('savsoft_result');
        $quiz = $query->row_array();
        if($quiz['quid'] == "0") {
            $quiz['quiz_name'] = $quiz['practice_name'];
            $quiz['noq'] = $quiz['practice_noq'];
            $quiz['duration'] = $quiz['practice_time'];
            $quiz['pass_percentage'] = PRACTICE_PASS;
            $quiz['maximum_attempts'] = "--";
        }
        return $quiz;
    }

    public function get_result_list_by_uid($uid){
		$this->db->where('savsoft_result.uid', $uid);
		$this->db->order_by('rid','desc');
		$query=$this->db->get('savsoft_result');
		return $query->result_array();
	}

    public function get_result_list_by_uid_and_gid($uid, $gid, $category){
		$this->db->where("FIND_IN_SET('" . $gid . "', gids)");
		$this->db->where('savsoft_result.uid', $uid);
        $this->db->or_group_start();
        $this->db->where("FIND_IN_SET('" . $category . "', categories)");
        $this->db->where('savsoft_result.quid', 0);
        $this->db->where('savsoft_result.uid', $uid);
        $this->db->group_end();
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
		$this->db->order_by('rid','desc');
		$query=$this->db->get('savsoft_result');
		return $query->result_array();
	}

    public function get_result_by_user($uid, $gid, $category, $index = -1)
    {
        $list = $this->get_result_list_by_uid_and_gid($uid, $gid, $category);
        $result = [];
        $unique_wrong = [];
		$query = $this->db->where('savsoft_answers.uid', $uid)->group_by('savsoft_answers.qid')->where("savsoft_result.result_status != 'Open'")->where("savsoft_result.result_status != 'Cancel'")->group_by('savsoft_answers.rid')->join('savsoft_result', 'savsoft_result.rid = savsoft_answers.rid', 'left')->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left')->join('savsoft_qbank', 'savsoft_qbank.qid = savsoft_answers.qid', 'left')->select('*')->select_sum('score_u')->order_by('savsoft_answers.qid', 'DESC')->get('savsoft_answers');
		$data_questions = $query->result_array();
        $incorrect_arr = [];
        foreach($data_questions as $val) {
			if($val['score_u'] < 0.99) {
				$incorrect_arr[] = $val;
			}
		}
        //count incorrect
        $qid_incorrect_prev = 0;
        $incorrect_arr_count = [];
        $count = 0;
        $numItems = count($incorrect_arr);
        $i = 0;
        foreach ($incorrect_arr as $val) {
            $i++;
            if($qid_incorrect_prev == 0) {
                $qid_incorrect_prev = $val['qid'];
                continue;
            }
            $count++;
            if($val['qid'] != $qid_incorrect_prev) {
                if($i === $numItems) {
                    $incorrect_arr_count[$qid_incorrect_prev] = $count;
                    $count = 0;
                    $incorrect_arr_count[$val['qid']] = 1;
                } else {
                    $incorrect_arr_count[$qid_incorrect_prev] = $count;
                    $count = 0;
                    $qid_incorrect_prev = $val['qid'];
                }
            } elseif($i === $numItems) {
                $incorrect_arr_count[$qid_incorrect_prev] = $count + 1;
            }
        }
        arsort($incorrect_arr_count);
        $wrong_ans_arr = [];
        $wrong_ans_rid = [];
        $wrong_ans_quid = [];
        foreach($list as $value => $key){
            //Tim Cau hoi sai
            $score_each_question = explode(",",$key['score_individual']);
            $wrong_index = [];
            $each_question = explode(",",$key['r_qids']);
            foreach($score_each_question as $score => $each){
                if($each == "2" && !in_array($each_question[$score], $wrong_ans_arr)){
                    array_push($wrong_index ,$score);
                    array_push($wrong_ans_arr,$each_question[$score]);
                    array_push($wrong_ans_rid,$key['rid']);
                    array_push($wrong_ans_quid,$key['quid']);
                }
            }
        }
        //info cac cau sai
        foreach($wrong_ans_arr as $question => $info){
            $get_question = $this->quiz_model->get_questions_exam_tool($info);
            if($get_question != null){
                //get right ans
                $right_ans = [];
                $get_options = $this->quiz_model->get_options_exam_tool($info);
                $get_question[0]["options"] = $get_options;
                foreach($get_options as $option=>$opt_info){
                    if($opt_info["score"] > 0){
                        array_push($right_ans ,$opt_info["oid"]);
                    }
                }
                $right_ans = implode(",",$right_ans);
                $get_question[0]["correct"] = $right_ans;
                //user option
                $get_answered = $this->quiz_model->saved_answers($wrong_ans_rid[$question]);
                $user_options = [];
                foreach($get_answered as $answered=>$ans_info){
                    if($ans_info["qid"] == $info){
                        array_push($user_options ,$ans_info["q_option"]);
                    }
                }
                $user_options = implode(",",$user_options);
                $get_question[0]["userAns"] = $user_options;
                if($incorrect_arr_count[$get_question[0]["qid"]] == null)
                $get_question[0]["count"] = 1;
                else
                $get_question[0]["count"] = $incorrect_arr_count[$get_question[0]["qid"]];
                $get_question[0]["quid"] = $wrong_ans_quid[$question];
                $get_question[0]["rid"] = $wrong_ans_rid[$question];
                $difficultyStr = $this->result_model->getDifficultyStr($get_question[0]);
                $get_question[0]["difficulty"] = $difficultyStr;
                //day vao 1 mang
                array_push($result ,$get_question[0]);
            }
        }
        return $result;
    }

    public function get_result_by_user_single_index($uid, $gid, $category, $index, $search = null)
    {
        $list = $this->get_result_list_by_uid_and_gid($uid, $gid, $category);

        // Lấy dữ liệu câu trả lời sai trong một truy vấn
        $query = $this->db->where('savsoft_answers.uid', $uid)
            ->group_by('savsoft_answers.qid')
            ->where("savsoft_result.result_status != 'Open'")
            ->where("savsoft_result.result_status != 'Cancel'")
            ->group_by('savsoft_answers.rid')
            ->join('savsoft_result', 'savsoft_result.rid = savsoft_answers.rid', 'left')
            ->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left')
            ->join('savsoft_qbank', 'savsoft_qbank.qid = savsoft_answers.qid', 'left')
            ->select('*')
            ->select_sum('score_u')
            ->order_by('savsoft_answers.qid', 'DESC')
            ->get('savsoft_answers');

        $data_questions = $query->result_array();
        $incorrect_arr = array_filter($data_questions, function($val) {
            return $val['score_u'] < 0.99;
        });

        // Đếm số lần trả lời sai cho mỗi câu hỏi
        $incorrect_arr_count = [];
        foreach ($incorrect_arr as $val) {
            $qid = $val['qid'];
            if (!isset($incorrect_arr_count[$qid])) {
                $incorrect_arr_count[$qid] = 0;
            }
            $incorrect_arr_count[$qid]++;
        }
        arsort($incorrect_arr_count);

        // Thu thập các câu hỏi sai
        $wrong_questions = [];
        $processed_qids = [];

        foreach($list as $key) {
            $score_each_question = explode(",", $key['score_individual']);
            $each_question = explode(",", $key['r_qids']);

            foreach($score_each_question as $score => $each) {
                if($each == "2" && !in_array($each_question[$score], $processed_qids)) {
                    $qid = $each_question[$score];
                    $processed_qids[] = $qid;

                    // Lấy thông tin câu hỏi
                    $question_data = $this->quiz_model->get_question($qid);

                    // Apply search filter if provided
                    if($search && !empty($search)) {
                        if(stripos($question_data['question'], $search) === false) {
                            continue; // Skip this question if it doesn't match search
                        }
                    }

                    $options = $this->quiz_model->get_options($qid);
                    $question_data["options"] = $options;

                    // Lấy câu trả lời đúng
                    $right_ans = [];
                    foreach($options as $opt_info) {
                        if($opt_info["score"] > 0) {
                            $right_ans[] = $opt_info["oid"];
                        }
                    }
                    $question_data["correct"] = implode(",", $right_ans);

                    // Lấy câu trả lời của người dùng
                    $user_options = [];
                    $saved_answers = $this->quiz_model->saved_answers($key['rid']);
                    foreach($saved_answers as $ans) {
                        $user_options[] = $ans['q_option'];
                    }
                    $question_data["userAns"] = implode(",", $user_options);

                    // Thêm thông tin bổ sung
                    $question_data["count"] = $incorrect_arr_count[$qid] ?? 1;
                    $question_data["quid"] = $key['quid'];
                    $question_data["rid"] = $key['rid'];

                    // Lấy độ khó
                    $difficultyStr = $this->getDifficultyStr($question_data);
                    $question_data["difficulty"] = $difficultyStr;

                    $wrong_questions[] = $question_data;
                }
            }
        }

        // If index is -1, return all filtered results
        if($index == -1) {
            return $wrong_questions;
        }

        // If specific index requested, return that question
        if(isset($wrong_questions[$index])) {
            return [$wrong_questions[$index]];
        }

        return null; // Index not found
    }

    public function delete_bookmark($array,$uid)
	{
		//$array = [9766,9462];
        $this->db->where('uid', $uid);
        $data = $this->db->get('savsoft_result');
        $result = $data->result_array();
		foreach($result as $key => $value){
			$saved_arr = explode(",",$value['saved_r']);
			$qid_arr = explode(",",$value['r_qids']);
			foreach($saved_arr as $k => $val){
				if(in_array($qid_arr[$val], $array) && $val != "" && $val != null){
					$this->db->query("UPDATE `savsoft_result` SET saved_r = REPLACE(REPLACE(CONCAT(',', saved_r, ','), ',$val,', ','), ',,', ',') WHERE rid = '" . $value['rid'] . "'");
				}
			}
			
		}
	}

    public function add_bookmark($qid,$uid,$rid)
	{
		//$array = [9766,9462];
        $this->db->where('uid', $uid);
        $this->db->where('rid', $rid);
        $data = $this->db->get('savsoft_result');
        $result = $data->row();
        $saved_arr = explode(",",$result->saved_r);
		$qid_arr = explode(",",$result->r_qids);
        $key = array_search($qid, $qid_arr);
        if(in_array((string)$key, $saved_arr, true) == false){
            array_push($saved_arr,(string) $key);
            $new_array = [];
            foreach ($saved_arr as $value) {
                if ($value !== "null" && $value !== "") {
                    $new_array[] = $value;
                }
            }
            $saved_arr_str = implode(",",$new_array);
            $this->db->where('rid', $rid);
            $this->db->set('saved_r', $saved_arr_str);
            $this->db->update('savsoft_result');
        }
	}

    public function getQidBookmarkList($array){
        $new_array = [];
        foreach($array as $key => $value){
            array_push($new_array,$value["qid"]);
        }
        return implode(",",$new_array);
    }

    public function getDifficultyStr($val){
        $difficultyStr = "";
        if(intval($val['no_time_corrected']) == 0 || (intval($val['no_time_corrected']) + intval($val['no_time_incorrected'])) < 10){
            return "medium";
        }
        $difficulty = intval($val['no_time_corrected'])  / (intval($val['no_time_served']) - intval($val['no_time_unattempted']));
        if ($difficulty >= 0.85 && $difficulty <= 1) {
            $difficultyStr = "easy";
        } else if ($difficulty >= 0.7 && $difficulty <= 0.84) {
            $difficultyStr = "medium";
        } else if ($difficulty >= 0.4 && $difficulty <= 0.69) {
            $difficultyStr = "hard";
        } else{
            $difficultyStr = "very_hard";
        }
        return $difficultyStr;
    }

    public function check_user_result($uid, $rid){
        $result = $this->db->where('uid', $uid)->where('rid', $rid)->select('rid')->get('savsoft_result')->result_array();
        if(count($result) > 0) {
            return true;
        } else {
            return false;
        }
    }

    function update_time_left($data) {
        $rid = $data['id'];
        $time = $data['timeLeft'];
        try {
            $this->db->where('rid', $rid)->update('savsoft_result', array('time_left' => $time));
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function count_user_exam_quiz($uid, $gid)
    {
        $this->db->where('uid', $uid);
        $this->db->where('result_status !=', "Open");
        $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
        $this->db->where('savsoft_quiz.exam_quiz', 1);
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        $query = $this->db->get('savsoft_result');
        return $query->num_rows();
    }

    public function count_user_practice_quiz($uid, $gid, $category)
    {
        $this->db->group_start();
            $this->db->where('uid', $uid);
            $this->db->where("FIND_IN_SET('" . $gid . "', gids)");
            $this->db->where('exam_quiz', 0);
        $this->db->group_end();
        $this->db->or_group_start();
            $this->db->where("FIND_IN_SET('" . $category . "', categories)");
            $this->db->where('savsoft_result.quid', 0);
            $this->db->where('uid', $uid);
        $this->db->group_end();
        $this->db->where('result_status !=', "Open");
        $this->db->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left');
        
        $query = $this->db->get('savsoft_result');
        return $query->num_rows();
    }

    public function count_user_bookmark($uid)
    {
        $this->db->where('uid', $uid);
        $this->db->where('result_status !=', "Open");
        $this->db->select('r_qids, saved_r');
        $query = $this->db->get('savsoft_result');
        $results = $query->result_array();
        $questionIds = [];
        foreach ($results as $key => $value) {
            $r_qids = explode(',', $value['r_qids']);
            $saved_r = explode(',', $value['saved_r']);
            $saved_r = array_filter($saved_r, function($value) {
                return $value !== "null";
            });
            foreach ($saved_r as $key => $value) {
                $questionIds[] = $r_qids[$value];
            }
        }
        $questionIds = array_unique($questionIds);

        return count($questionIds);
    }

    public function count_user_note($uid)
    {
        $this->db->where('uid', $uid);
        $this->db->select('id');
        $query = $this->db->get('question_note');
        return count($query->result_array());
    }

    public function get_current_streak($user_id) {
        // Lấy danh sách các ngày làm bài riêng biệt
        $this->db->select('DISTINCT DATE(end_time) as completion_date', FALSE);
        $this->db->where('uid', $user_id);
        $this->db->where('result_status !=', "Open");
        $this->db->order_by('completion_date', 'DESC');
        $query = $this->db->get('savsoft_result');

        $dates = array();
        foreach ($query->result() as $row) {
            $dates[] = date("Y-m-d\TH:i:s\Z", $row->completion_date);
        }

        if (empty($dates)) {
            return 0;
        }

        // Tìm chuỗi ngày liên tiếp gần nhất
        $currentStreak = 1; // Bắt đầu với ngày hiện tại/gần nhất
        $today = date('Y-m-d');

        // Nếu ngày làm bài gần nhất không phải là hôm nay, kiểm tra xem có phải hôm qua không
        if ($dates[0] != $today) {
            $dayDiff = (strtotime($today) - strtotime($dates[0])) / (60 * 60 * 24);
            if ($dayDiff > 1) {
                // Nếu đã qua hơn 1 ngày kể từ lần làm bài cuối, streak đã bị đứt
                $currentStreak = 0;
            }
        }

        // Duyệt qua danh sách các ngày làm bài
        for ($i = 0; $i < count($dates) - 1; $i++) {
            $currentDate = strtotime($dates[$i]);
            $nextDate = strtotime($dates[$i+1]);

            // Tính số ngày giữa hai ngày làm bài liên tiếp
            $dayDiff = ($currentDate - $nextDate) / (60 * 60 * 24);

            if ($dayDiff == 1) {
                // Nếu là ngày liên tiếp
                $currentStreak++;
            } else if ($dayDiff > 1) {
                // Nếu có khoảng cách > 1 ngày, streak đã bị đứt
                break;
            }
        }

        return $currentStreak;
    }

    public function count_user_question_by_difficulty($qids, $incorrectQids) {
        $getQuestion = $this->quiz_model->get_questions_exam_tool(implode(',', $qids));
        $countEasy = 0;
        $countMedium = 0;
        $countHard = 0;
        $countVeryHard = 0;
        $idEasy = [];
        $idMedium = [];
        $idHard = [];
        $idVeryHard = [];
        $countIncorrectEasy = 0;
        $countIncorrectMedium = 0;
        $countIncorrectHard = 0;
        $countIncorrectVeryHard = 0;
        if($getQuestion != null){
            //get right ans
            foreach ($getQuestion as $key => $value) {
                $difficultyStr = $this->result_model->getDifficultyStr($value);
                if ($difficultyStr == 'easy') {
                    $countEasy++;
                    $idEasy[] = $value['qid'];
                } elseif ($difficultyStr == 'medium') {
                    $countMedium++;
                    $idMedium[] = $value['qid'];
                } elseif ($difficultyStr == 'hard') {
                    $countHard++;
                    $idHard[] = $value['qid'];
                } elseif ($difficultyStr == 'very_hard') {
                    $countVeryHard++;
                    $idVeryHard[] = $value['qid'];
                }
            }
        }
        foreach ($incorrectQids as $key => $value) {
            if (in_array($value, $idEasy)) {
                $countIncorrectEasy++;
            } elseif (in_array($value, $idMedium)) {
                $countIncorrectMedium++;
            } elseif (in_array($value, $idHard)) {
                $countIncorrectHard++;
            } elseif (in_array($value, $idVeryHard)) {
                $countIncorrectVeryHard++;
            }
        }
        $result = [
            "easy" => ["attempted" => $countEasy, "wrong" => $countIncorrectEasy],
            "medium" => ["attempted" => $countMedium, "wrong" => $countIncorrectMedium],
            "hard" => ["attempted" => $countHard, "wrong" => $countIncorrectHard],
            "very_hard" => ["attempted" => $countVeryHard, "wrong" => $countIncorrectVeryHard]
        ];
        return $result;
    }

    public function get_quiz_avg_score($uid, $tests, $continueQuiz = false) {
        $result = [];
        $quids = array_map(function($test) {
            return $test['quid'];
        }, $tests);
        if(count($quids) == 0){
            return 0;
        }
        $this->db->select("*,COUNT(*) as count, AVG(percentage_obtained) as avg");
        if ($continueQuiz == false) {
            $this->db->where('result_status !=', "Open");
        }
        $this->db->group_by('quid');
        $query = $this->db->where('uid', $uid)->where_in('quid', $quids)->get('savsoft_result')->result_array();
        foreach ($query as $key => $value) {
            $result[] = array("quid" => $value['quid'], "avg" => number_format((float)$value['avg'], 0, '.', ''), "count" => $value['count']);
        }
        return $result;
    }
}
