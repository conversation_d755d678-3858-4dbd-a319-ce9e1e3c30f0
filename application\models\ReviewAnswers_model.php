<?php
Class ReviewAnswers_model extends CI_Model
{
    function wrongAnswer_list_api($search = 'what topics'){
        $logged_in = $this->session->userdata('logged_in');
        if ($logged_in['su'] == '2') {
            $uid = $logged_in['uid'];
        }
        $data['uid'] = $uid;
        // getting questions
        $query4 = $this->db->where('savsoft_answers.uid', $uid)->group_by('savsoft_answers.qid')->like('savsoft_qbank.question', $search)->where("savsoft_result.result_status != 'Open'")->where("savsoft_result.result_status != 'Cancel'")->join('savsoft_result', 'savsoft_result.rid = savsoft_answers.rid', 'left')->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left')->join('savsoft_qbank', 'savsoft_qbank.qid = savsoft_answers.qid', 'left')->select('*')->select_sum('score_u')->select('count(*) as count')->order_by('savsoft_answers.aid', 'DESC')->get('savsoft_answers');
        $data_questions = $query4->result_array();
        $new_data_questions = [];
        foreach($data_questions as $val) {
            if(!in_array($val['qid'],$new_data_questions)){
                array_push($new_data_questions,$val['qid']);
                if($val['score_u'] < 0.99) {
                    $incorrect_arr[] = $val;
                } 
            }
        }
        /* foreach($data_questions as $val) {
            if($val['score_u'] < 0.99) {
                $incorrect_arr[] = $val;
            } 
        } */
		return $incorrect_arr;
    }

    function getBookmarked_Ans($uid, $gid, $category, $index = -1, $search = null){
        $list = $this->result_model->get_result_list_by_uid_and_gid($uid, $gid, $category);
        $result = [];
        $unique_bookmark = [];

        // Collect all bookmarked questions first
        foreach($list as $key){
            $mark_question = array_filter(explode(",", $key['saved_r']));
            $mark_index = array_unique(array_filter($mark_question, function($val) {
                return $val !== "null" && $val !== "";
            }));

            $each_question = explode(",", $key['r_qids']);

            foreach($mark_index as $question_index) {
                if (!isset($each_question[$question_index])) continue;

                $qid = $each_question[$question_index];
                if (in_array($qid, $unique_bookmark)) continue;

                $unique_bookmark[] = $qid;

                $question_data = $this->quiz_model->get_question($qid);

                // Apply search filter if provided
                if($search && !empty($search)) {
                    if(stripos($question_data['question'], $search) === false) {
                        continue; // Skip this question if it doesn't match search
                    }
                }

                $options = $this->quiz_model->get_options($qid);
                $question_data["options"] = $options;

                // Get right answers
                $right_ans = [];
                foreach($options as $opt_info){
                    if($opt_info["score"] > 0){
                        $right_ans[] = $opt_info["oid"];
                    }
                }
                $question_data["correct"] = implode(",", $right_ans);

                // Get user answers
                $user_options = [];
                $saved_answers = $this->quiz_model->saved_answers($key['rid']);
                foreach($saved_answers as $ans){
                    $user_options[] = $ans['q_option'];
                }
                $question_data["userAns"] = implode(",", $user_options);
                $question_data["quid"] = $key["quid"];

                // Get difficulty
                $difficultyStr = $this->result_model->getDifficultyStr($question_data);
                $question_data["difficulty"] = $difficultyStr;

                $result[] = $question_data;
            }
        }

        // If index is -1, return all filtered results
        if($index == -1) {
            return $result;
        }

        // If specific index requested, return that question
        if(isset($result[$index])) {
            return $result[$index];
        }

        return null; // Index not found
    }
}	
?>
