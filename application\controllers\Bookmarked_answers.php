<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Bookmarked_answers extends CI_Controller
{
	function __construct()
	{
		parent::__construct();
		$this->load->database();
		$this->load->helper('url');
		$this->load->model("language_model");
        $this->load->model("studymaterial_model");
		$this->load->model("quiz_model");
		$this->load->model("report_model");
		$this->load->model("result_model");
		$this->load->model("user_model");
		$this->load->model("note_model");
		$this->load->model("comment_model");
		$this->load->model("ReviewAnswers_model");
		$this->load->helper('cookie');
		$this->load->helper('curl_helper');
		$this->lang->load('basic', 
        $this->language_model->get());
		$this->load->helper('form');
		if (!$this->session->userdata('logged_in')) {
			$this->session->set_userdata('referred_from', current_url());
			redirect('login');
		}
		$logged_in = $this->session->userdata('logged_in');
		if ($logged_in['base_url'] != base_url()) {
			$this->session->set_userdata('referred_from', current_url());
			$this->session->unset_userdata('logged_in');
			redirect('login');
		}
		if($this->user_model->user_status_by_id($logged_in['uid']) ==  false){
			$this->session->set_userdata('referred_from', current_url());
			$this->session->unset_userdata('logged_in');
			$this->session->set_flashdata('message', $this->lang->line("account_inactive"));
			redirect('login');
		}
		$subscriptionExpired = $this->user_model->get_subscription_expired($logged_in['uid']);
	
		if ($subscriptionExpired != 0) {
			$currentDate = date('Y-m-d');
			$subscriptionExpiredDate = date('Y-m-d', $subscriptionExpired);
	
			if ($subscriptionExpiredDate < $currentDate) {
				$this->user_model->update_status_user($logged_in['uid'], 'Inactive');
				$this->session->set_userdata('referred_from', current_url());
				$this->session->unset_userdata('logged_in');
				$this->session->set_flashdata('message', $this->lang->line("subscription_expired_message"));
				redirect('login');
			}
		}
	}
	public function index()
	{
		add_js_footer('/components/feature_note.js');
		add_js(['/pages/result.js']);
		add_css(['/pages/result.scss']);
		$logged_in = $this->session->userdata('logged_in');
		$userData = $this->user_model->get_user($logged_in['uid']);
    	$uid = $logged_in['uid'];
        $data['uid'] = $uid;
		$gid = $userData['gid'];
		$this->user_model->check_user_validate_group();
		$category = $this->user_model->getCategoryByGroup($gid);
		// CAC CAU BOOKMARK
		// getting questions
		$result = $this->ReviewAnswers_model->getBookmarked_Ans($uid, $gid, $category['name']);
		$arr_report = [];
		$arr_report_rid = [];
		$report_list = $this->report_model->list_rid($uid);
		foreach ($report_list as $key => $value) {
			array_push($arr_report, $value["qid"]);
			array_push($arr_report_rid, $value["report_id"]);
		}
		//lay array cau da bao cao
		$data["arr_report_rid"] = $arr_report_rid;
		$data["arr_report"] = $arr_report;
		$data["result"] = $result;
		$data["title"] =  $this->lang->line("review");
		$data["logged_in"] = $logged_in;
		//CAC CAU SAI
		$list = $this->result_model->get_result_by_user($uid, $gid, $category['name']);
		usort($list, function ($a, $b) {
			return $b['count'] <=> $a['count']; // Descending order - wrong answers
		});
		$data["result2"] = $list;
		
		//lay danh sach comment
		$qids = $this->comment_model->get_qids_from_2_lists($result, $list);
		$lists = $this->comment_model->get_question_from_2_lists($result, $list);
		$data['questions'] = $lists;
		$comments_by_qids = $this->quiz_model->get_comment($qids);
		$data['comments_by_qids'] = $comments_by_qids;
		// end lay danh sach comment
		$wrong_qid_list = [];
		foreach ($list as $key => $value) {
			array_push($wrong_qid_list, $value["qid"]);
		}
		$data["bookmark_qid_list"] = $this->result_model->getQidBookmarkList($result);
		$data['notes'] = $this->note_model->get_list_note_user($category['cid']);
		$data['question_notes'] = $this->note_model->get_question_by_list_note(array_values($data['notes']));
		$data["wrong_qid_list"] = implode(",",$wrong_qid_list);
		if(count($data['result']) == 0 || count($data['result2']) == 0) {
			$uid = $logged_in['uid'];
			$user = $this->user_model->get_user($uid);
			$userCategory = $this->db->where("FIND_IN_SET('".$user['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
			$data['category'] = $userCategory;
			foreach (DOMAINS as $group => $domainValue) {
				if ($this->user_model->check_user_in_group($uid, $group)) {
					$data['show_assist_box_' . $group] = true;
				}else{
					$data['show_assist_box_' . $group] = false;
				}
			}
		}

		$translate_mode = $this->user_model->get_translate_mode($logged_in['uid']);
		$data['translate_mode'] = $translate_mode;

		$report_content = $this->load->view('custom/report_modal', [], true);
		$report_content = str_replace(array("\n", "\r", "\t"), '', $report_content);
		$data["report_content"] = $report_content;
		$this->load->view('header_new', $data);
		$this->load->view('pages/list_bookmark', $data);
		$this->load->view('footer_new', $data);
	}

	public function get_bookmark_questions()
	{
		$index = $this->input->get('index');
		$search = $this->input->get('search');
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$gid = $logged_in['gid'];
		$category = $this->user_model->getCategoryByGroup($gid);
		$result = $this->ReviewAnswers_model->getBookmarked_Ans($uid, $gid, $category['name'], $index, $search);
		echo json_encode($result);
	}

	public function get_wrong_questions()
	{
		$index = $this->input->get('index');
		$search = $this->input->get('search');
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$gid = $logged_in['gid'];
		$category = $this->user_model->getCategoryByGroup($gid);
		$result = $this->result_model->get_result_by_user_single_index($uid, $gid, $category['name'], $index, $search);
		echo json_encode($result);
	}

	public function get_note_questions()
	{
		$index = $this->input->get('index');
		$search = $this->input->get('search');
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$gid = $logged_in['gid'];
		$category = $this->user_model->getCategoryByGroup($gid);
		$notes = $this->note_model->get_list_note_user($category['cid'], $index, $search);
		$result = $this->note_model->get_question_by_list_note(array_values([$notes]), $search);
		echo json_encode($result);
	}

	public function un_bookmark()
	{
		$logged_in = $this->session->userdata('logged_in');
        if ($logged_in['su'] == '2') {
            $uid = $logged_in['uid'];
			$qid = explode(" ",$this->input->post('qid'));
			try {
				// $this->result_model->delete_bookmark($qid,$uid);
				$this->result_model->delete_bookmark($qid,$uid);
				print_r("success");
			} catch (Exception $e) {
				print_r("error");
			}
        }else{
			echo "error";
		}

	}

	public function add_bookmark()
	{
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$qid =$this->input->post('qid');
		$rid =$this->input->post('rid');
		try {
			// $this->result_model->add_bookmark($qid,$uid,$rid);
			$this->result_model->add_bookmark($qid,$uid,$rid);
			print_r("success");
		} catch (Exception $e) {
			print_r("error");
		}
	}
}
